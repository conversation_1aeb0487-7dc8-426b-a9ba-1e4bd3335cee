export interface LangContentStructure {
    experienceRoadmap: {
        imageAlt: string;
        description: string;
        subtitle: string;
        loadingText: string;
        legend: {
            startEnd: string;
            foundations: string;
            suthub: string;
            klever: string;
            midas: string;
            atomic: string;
        };
    };
    projectsList: {
        imageAlt: string;
        description: string;
        viewProject: string;
        projects: {
            soraizReservas: {
                title: string;
                description: string;
            };
            eventia: {
                title: string;
                description: string;
            };
            ibiCash: {
                title: string;
                description: string;
            };
            midasGestor: {
                title: string;
                description: string;
            };
            cryptoTracker: {
                title: string;
                description: string;
            };
            regenteApp: {
                title: string;
                description: string;
            };
        };
    };
    smartOverview: {
        imageAlt: string;
        description: string;
        questionLabel: string;
        questionPlaceholder: string;
        examplesTitle: string;
        responseText: string;
        exampleQuestions: {
            experience: string;
            technologies: string;
            projects: string;
        };
    };
}

export { ENGLISH_CONTENT } from "./eng"
export { GERMAN_CONTENT } from "./ger"
export { PORTUGUESE_CONTENT } from "./ptbr"