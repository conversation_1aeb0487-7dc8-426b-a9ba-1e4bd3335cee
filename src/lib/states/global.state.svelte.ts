import { ENGLISH_CONTENT, GERMAN_CONTENT, PORTUGUESE_CONTENT, type LangContentStructure } from "$lib/langs";
import { match } from "ts-pattern";

function globalState() {
    type Langs = "en" | "pt" | "ger";
    const ui = $state({
        currentLang: <Langs>"en",
    })

    const texts = $derived((): LangContentStructure => {
        return match(ui.currentLang)
            .with("en", () => ENGLISH_CONTENT)
            .with("pt", () => PORTUGUESE_CONTENT)
            .with("ger", () => GERMAN_CONTENT)
            .exhaustive()
    })

    function setLang(lang: Langs) {
        ui.currentLang = lang
    }

    return {
        ui,
        texts,
        setLang
    }
}

export const globals = globalState()