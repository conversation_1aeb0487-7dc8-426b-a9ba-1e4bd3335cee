export class DiscordNotifier {
    private appName: string;
    private webhookUrl: string;

    constructor(webhookUrl: string, appName: string) {
        this.webhookUrl = webhookUrl;
        this.appName = appName;
    }

    async sendNotification(messages: { title: string; message: string, color?: string }[]) {
        const embeds = messages.map(({ title, message, color }) => ({
            appName: this.appName || 'My App',
            title,
            description: message,
            color: color || 0x0099ff
        }));

        const payload = {
            embeds
        };

        const response = await fetch(this.webhookUrl, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(payload)
        });

        if (!response.ok) {
            throw new Error(`Discord webhook failed: ${response.statusText}`);
        }
    }
}