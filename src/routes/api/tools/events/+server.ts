import { DiscordNotifier } from "$lib/tools/discord";
import { json } from '@sveltejs/kit';
import type { RequestHandler } from './$types';
import { DISCORD_WEBHOOK_URL } from '$env/static/private';

export const POST: RequestHandler = async ({ request }) => {
    try {
        const { messages } = await request.json();

        if (!messages || !Array.isArray(messages)) {
            return json({ error: 'Messages array is required' }, { status: 400 });
        }

        for (const message of messages) {
            if (!message.title || !message.message) {
                return json({ error: 'Each message must have title and message properties' }, { status: 400 });
            }
        }

        const notifier = new DiscordNotifier(DISCORD_WEBHOOK_URL, import.meta.env.VITE_APP_NAME);
        await notifier.sendNotification(messages);

        return json({ success: true, message: 'Notifications sent successfully' });
    } catch (error) {
        return json({ error: 'Failed to send notifications' }, { status: 500 });
    }
};