<div class="rasengan">
	<div class="line line1"></div>
	<div class="line line2"></div>
	<div class="line line3"></div>
	<div class="line line4"></div>
	<div class="line line5"></div>
	<div class="line line6"></div>
	<div class="line line7"></div>
	<div class="line line8"></div>
	<div class="line line9"></div>
	<div class="line line10"></div>
	<div class="line line11"></div>
	<div class="line line12"></div>
	<div class="line line13"></div>
	<div class="line line14"></div>
	<div class="line line15"></div>
	<div class="line line16"></div>
	<div class="line line17"></div>
	<div class="line line18"></div>
	<div class="line line19"></div>
	<div class="line line20"></div>
</div>

<style>
	.rasengan {
		position: relative;
		width: 20px;
		height: 20px;
		perspective: 100px;
		background: radial-gradient(#1a1a1a 5%, #0a0a0a 40%);
		border-radius: 50%;
		animation: twinkling 1.5s infinite linear;
	}

	.rasengan .line {
		position: absolute;
		border: 1px solid rgba(64, 64, 64, 0.5);
		border-radius: 50%;
		box-shadow: 0 0 1px #404040;
	}

	/* Individual line styles with varying sizes and animation delays */
	.line1 {
		top: 5px;
		left: 5px;
		bottom: 5px;
		right: 5px;
		animation: rotating 0.03s infinite linear;
	}
	.line2 {
		top: 2.5px;
		left: 2.5px;
		bottom: 2.5px;
		right: 2.5px;
		animation: rotating 0.06s infinite linear;
	}
	.line3 {
		top: 7.5px;
		left: 7.5px;
		bottom: 7.5px;
		right: 7.5px;
		animation: rotating 0.09s infinite linear;
	}
	.line4 {
		top: 1.3px;
		left: 1.3px;
		bottom: 1.3px;
		right: 1.3px;
		animation: rotating 0.12s infinite linear;
	}
	.line5 {
		top: 8.7px;
		left: 8.7px;
		bottom: 8.7px;
		right: 8.7px;
		animation: rotating 0.15s infinite linear;
	}
	.line6 {
		top: 3.8px;
		left: 3.8px;
		bottom: 3.8px;
		right: 3.8px;
		animation: rotating 0.18s infinite linear;
	}
	.line7 {
		top: 6.2px;
		left: 6.2px;
		bottom: 6.2px;
		right: 6.2px;
		animation: rotating 0.21s infinite linear;
	}
	.line8 {
		top: 9.9px;
		left: 9.9px;
		bottom: 9.9px;
		right: 9.9px;
		animation: rotating 0.24s infinite linear;
	}
	.line9 {
		top: 1.7px;
		left: 1.7px;
		bottom: 1.7px;
		right: 1.7px;
		animation: rotating 0.27s infinite linear;
	}
	.line10 {
		top: 8px;
		left: 8px;
		bottom: 8px;
		right: 8px;
		animation: rotating 0.3s infinite linear;
	}
	.line11 {
		top: 4.6px;
		left: 4.6px;
		bottom: 4.6px;
		right: 4.6px;
		animation: rotating 0.33s infinite linear;
	}
	.line12 {
		top: 7px;
		left: 7px;
		bottom: 7px;
		right: 7px;
		animation: rotating 0.36s infinite linear;
	}
	.line13 {
		top: 3.1px;
		left: 3.1px;
		bottom: 3.1px;
		right: 3.1px;
		animation: rotating 0.39s infinite linear;
	}
	.line14 {
		top: 9.4px;
		left: 9.4px;
		bottom: 9.4px;
		right: 9.4px;
		animation: rotating 0.42s infinite linear;
	}
	.line15 {
		top: 2.1px;
		left: 2.1px;
		bottom: 2.1px;
		right: 2.1px;
		animation: rotating 0.45s infinite linear;
	}
	.line16 {
		top: 5.8px;
		left: 5.8px;
		bottom: 5.8px;
		right: 5.8px;
		animation: rotating 0.48s infinite linear;
	}
	.line17 {
		top: 4.1px;
		left: 4.1px;
		bottom: 4.1px;
		right: 4.1px;
		animation: rotating 0.51s infinite linear;
	}
	.line18 {
		top: 8.2px;
		left: 8.2px;
		bottom: 8.2px;
		right: 8.2px;
		animation: rotating 0.54s infinite linear;
	}
	.line19 {
		top: 6.8px;
		left: 6.8px;
		bottom: 6.8px;
		right: 6.8px;
		animation: rotating 0.57s infinite linear;
	}
	@keyframes rotating {
		0% {
			transform: rotateY(0);
		}
		100% {
			transform: rotateZ(360deg) rotateX(360deg) rotateY(360deg);
		}
	}

	@keyframes twinkling {
		0%,
		100% {
			box-shadow:
				0 0 1px 0px #222,
				0 0 8px 2px #111;
		}
		20% {
			box-shadow:
				0 0 1px 0px #222,
				0 0 6px 2px #111;
		}
		40% {
			box-shadow:
				0 0 2px 0px #222,
				0 0 5px 2px #111;
		}
		60% {
			box-shadow:
				0 0 1px 0px #222,
				0 0 8px 2px #111;
		}
		80% {
			box-shadow:
				0 0 2px 0px #222,
				0 0 6px 2px #111;
		}
	}
</style>
