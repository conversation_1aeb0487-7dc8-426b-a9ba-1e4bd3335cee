<script lang="ts">
	import Icon from '$lib/components/ui/icon.svelte';
	import { Input } from '$lib/components/ui/input';
	import { Label } from '$lib/components/ui/label';
	import { fade } from 'svelte/transition';
	import { globals } from '$lib/states/global.state.svelte';

	let userQuestion = $state('');

	function handleExampleClick(question: string) {
		userQuestion = question;
	}

	function handleSubmit() {
		if (!userQuestion.trim()) return;
		console.log('Pergunta enviada:', userQuestion);
	}
</script>

<div class="mt-6 space-y-6" in:fade>
	<div>
		<div class="flex justify-center">
			<div class="relative">
				<div
					class="size-32 overflow-hidden rounded-full border-2 border-white/20 bg-white/10 shadow-xl backdrop-blur-md"
				>
					<img
						src="https://ik.imagekit.io/og7loqgh2/holdin-ai.png?updatedAt=1751391136997"
						alt={globals.texts().smartOverview.imageAlt}
						class="h-full w-full object-cover"
					/>
				</div>
			</div>
		</div>

		<div class="mt-3 text-center">
			<p class="mx-auto max-w-md text-sm leading-relaxed text-gray-600">
				{globals.texts().smartOverview.description}
			</p>
		</div>
	</div>

	<div class="space-y-2">
		<Label for="question-input" class="text-sm font-semibold text-gray-700">
			{globals.texts().smartOverview.questionLabel}
		</Label>
		<div class="flex gap-2">
			<Input
				id="question-input"
				bind:value={userQuestion}
				placeholder={globals.texts().smartOverview.questionPlaceholder}
				class="flex-1 border-white/20 bg-white/20 backdrop-blur-md placeholder:text-gray-500"
				onkeydown={(e) => e.key === 'Enter' && handleSubmit()}
			/>
			<button
				onclick={handleSubmit}
				disabled={!userQuestion.trim()}
				class="font-heading relative flex w-fit cursor-pointer items-center overflow-hidden rounded-md bg-white/20 px-4 font-bold shadow-xl backdrop-blur-md transition-all before:absolute before:top-0 before:left-0 before:h-full before:w-[3px] before:rounded-l-md before:bg-blue-500/90 hover:bg-white/30 disabled:cursor-not-allowed disabled:opacity-50"
			>
				<Icon icon="material-symbols:send-rounded" class="text-lg" />
			</button>
		</div>
	</div>

	<div class="space-y-3">
		<h3 class="text-sm font-semibold text-gray-700">
			{globals.texts().smartOverview.examplesTitle}
		</h3>
		<div class="grid gap-2">
			{#each Object.values(globals.texts().smartOverview.exampleQuestions) as question}
				<button
					onclick={() => handleExampleClick(question)}
					class="font-heading relative w-full cursor-pointer overflow-hidden rounded-md border border-white/20 bg-white/10 p-3 text-left text-sm font-medium text-gray-700 shadow-md backdrop-blur-md transition-all before:absolute before:top-0 before:left-0 before:h-full before:w-[2px] before:rounded-l-md before:bg-gray-400/60 hover:bg-white/20 hover:shadow-lg hover:before:bg-blue-500/80"
				>
					{question}
				</button>
			{/each}
		</div>
	</div>

	<div class="rounded-lg border border-white/20 bg-white/10 p-4 backdrop-blur-md">
		<div class="flex items-center gap-2 text-gray-600">
			<Icon icon="streamline-flex:ai-scanner-robot-solid" class="text-lg" />
			<span class="text-sm font-medium">{globals.texts().smartOverview.responseText}</span>
		</div>
	</div>
</div>
