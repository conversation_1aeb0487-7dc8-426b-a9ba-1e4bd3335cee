<script lang="ts">
	import type { Snippet } from 'svelte';
	import * as Sheet from '$lib/components/ui/sheet';
	import { cn } from '$lib/utils';
	import Icon from './icon.svelte';

	interface HeaderConfig {
		show: boolean;
		position: 'default' | 'adaptable' | 'fixed';
	}

	interface FooterConfig {
		show: boolean;
		position: 'default' | 'adaptable' | 'fixed';
	}

	interface Props {
		mode: 'normal' | 'sidebar';
		header?: HeaderConfig;
		footer?: FooterConfig;

		headerSnippet?: Snippet;
		sidebarSnippet?: Snippet;
		footerSnippet?: Snippet;
		sheetTitleSnippet?: Snippet;
		children: Snippet;

		openSheet?: boolean;
		onSheetOpenChange?: (open: boolean) => void;
		onOpenSheet?: () => void;
	}

	let {
		mode = 'normal',
		header = { show: true, position: 'default' },
		footer = { show: true, position: 'default' },
		headerSnippet,
		sidebarSnippet,
		footerSnippet,
		sheetTitleSnippet,
		openSheet,
		onSheetOpenChange,
		onOpenSheet,
		children
	}: Props = $props();

	const hasHeader = $derived(header.show && headerSnippet);
	const hasFooter = $derived(footer.show && footerSnippet);
</script>

<main
	class={cn({
		'relative w-full overflow-x-hidden': mode === 'normal',
		'relative h-dvh min-h-dvh w-full overflow-hidden': mode === 'sidebar',
		'mt-15': (hasHeader && header.position === 'fixed') || header.position === 'adaptable'
	})}
>
	{#if headerSnippet}
		<header
			class={cn({
				'fixed top-0 right-0 z-50 w-full border-b bg-white/70 backdrop-blur-2xl':
					header.position === 'fixed',
				'fixed top-0 right-0 z-50 w-full bg-white lg:static': header.position === 'adaptable',
				default: header.position === 'default'
			})}
		>
			{@render headerSnippet()}
		</header>
	{/if}

	{#if mode === 'sidebar'}
		<header
			class="fixed top-0 right-0 left-0 z-50 flex h-14 items-center justify-between border-b bg-white px-4 lg:hidden"
		>
			{#if sheetTitleSnippet}
				{@render sheetTitleSnippet()}
			{/if}
			<button
				onclick={() => onOpenSheet?.()}
				class="flex items-center rounded-md p-2 hover:bg-gray-100"
			>
				<Icon icon="garden:menu-fill-12" class="text-lg" />
			</button>
		</header>

		<div class="grid size-full pt-14 lg:grid-cols-[auto_1fr] lg:pt-0">
			{#if sidebarSnippet}
				<aside class="hidden lg:block">
					{@render sidebarSnippet()}
				</aside>

				<Sheet.Root open={openSheet} onOpenChange={onSheetOpenChange}>
					<Sheet.Content side="right" class="w-auto p-0">
						{@render sidebarSnippet()}
					</Sheet.Content>
				</Sheet.Root>
			{/if}

			<div class="min-w-0">
				{@render children()}
			</div>
		</div>
	{:else}
		{@render children()}
	{/if}

	{#if hasFooter}
		<footer
			class={cn({
				'fixed right-0 bottom-0 z-50 w-full bg-white': footer.position === 'fixed',
				'fixed right-0 bottom-0 z-50 w-full bg-white lg:relative': footer.position === 'adaptable',
				relative: footer.position === 'default'
			})}
		>
			{@render footerSnippet?.()}
		</footer>
	{/if}
</main>