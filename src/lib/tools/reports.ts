export interface ReportData {
    title: string;
    description: string;
    severity: 'low' | 'medium' | 'high' | 'critical';
    category: 'error' | 'timeout' | 'warning' | 'info';
    metadata?: Record<string, any>;
    stackTrace?: string;
    userId?: string;
    functionName?: string;
}

export class ReportsService {
    private sessionId: string;

    constructor() {
        this.sessionId = Math.random().toString(36).substring(2) + Date.now().toString(36);
    }

    async report(
        title: string,
        description: string,
        severity: 'low' | 'medium' | 'high' | 'critical',
        options?: {
            functionName?: string;
            userId?: string;
            metadata?: Record<string, any>;
            error?: Error;
        }
    ) {
        const reportData: ReportData = {
            title,
            description,
            severity,
            category: this.getCategoryFromSeverity(severity),
            functionName: options?.functionName,
            userId: options?.userId,
            metadata: {
                ...options?.metadata,
                sessionId: this.sessionId,
                timestamp: new Date().toISOString()
            },
            stackTrace: options?.error?.stack
        };

        return this.sendReport(reportData);
    }

    private getCategoryFromSeverity(severity: string): 'error' | 'timeout' | 'warning' | 'info' {
        switch (severity) {
            case 'critical':
            case 'high':
                return 'error';
            case 'medium':
                return 'timeout';
            case 'low':
                return 'warning';
            default:
                return 'info';
        }
    }

    private async sendReport(reportData: ReportData) {
        const colors = {
            low: 0x00ff00,     // Green
            medium: 0xffaa00,  // Orange
            high: 0xff0000,    // Red
            critical: 0x8b0000 // Dark Red
        };

        const now = new Date();
        const formattedDate = `${now.getDate().toString().padStart(2, '0')}/${(now.getMonth() + 1).toString().padStart(2, '0')}/${now.getFullYear()} - ${now.getHours().toString().padStart(2, '0')}:${now.getMinutes().toString().padStart(2, '0')}:${now.getSeconds().toString().padStart(2, '0')}`;

        const messages = [
            {
                title: reportData.title,
                message: reportData.description,
                color: colors[reportData.severity]
            },
            {
                title: "📍 Function Details",
                message: `Function: ${reportData.functionName || 'Unknown'}`,
                color: colors[reportData.severity]
            },
            {
                title: "🌐 Location",
                message: `URL: ${window.location.href}`,
                color: colors[reportData.severity]
            },
            {
                title: "⏰ Timestamp",
                message: `Time: ${formattedDate}`,
                color: colors[reportData.severity]
            }
        ];

        try {
            const response = await fetch('/api/tools/events', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ messages })
            });

            if (!response.ok) {
                console.error('Failed to send report:', await response.text());
                throw new Error('Failed to send report');
            }

            return await response.json();
        } catch (error) {
            console.error('Error sending report:', error);
            throw error;
        }
    }
}

const reportsService = new ReportsService();
export default reportsService;