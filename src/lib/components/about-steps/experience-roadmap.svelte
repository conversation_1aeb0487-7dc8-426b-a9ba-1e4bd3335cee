<script lang="ts">
	import { onMount } from 'svelte';
	import mermaid from 'mermaid';
	import { fade } from 'svelte/transition';

	let mermaidContainer: HTMLElement | null = null;
	let scrollContainer: HTMLElement | null = null;
	let isLoading = true;
	let isDragging = false;
	let startX = 0;
	let startY = 0;
	let scrollLeft = 0;
	let scrollTop = 0;

	const mermaidConfig: any = {
		theme: 'default',
		startOnLoad: false,
		flowchart: {
			useMaxWidth: true,
			htmlLabels: true,
			curve: 'basis'
		}
	};

	const roadmapDefinition = `
    flowchart TD
      %% Início da Jornada - 2021
      Start([🎯 2021 - Início dos Estudos]) --> Basics[📖 Estudos Básicos]
      Basics --> HTML[🌐 HTML5 & Semântica]
      Basics --> CSS[🎨 CSS3 & Flexbox/Grid]
      Basics --> JS[⚡ JavaScript ES6+]
      
      HTML --> WebFund[🔧 Fundamentos Web]
      CSS --> WebFund
      JS --> WebFund
      WebFund --> FirstProjects[💻 Primeiros Projetos]
      FirstProjects --> Freelance[💼 Freelances para Amigos]
      
      %% Transição para Carreira - 2022
      Freelance --> Transition[🚀 Transição para Carreira]
      Transition --> SUTHUB([🏢 2022 - SUTHUB<br/>Primeiro Emprego])
      
      %% SUTHUB - Desenvolvimento
      SUTHUB --> VueEco[🌿 Ecossistema Vue.js]
      VueEco --> Vue[⚡ Vue.js & Composition API]
      VueEco --> Vuetify[🎯 Vuetify Material Design]
      VueEco --> VueRouter[🛣️ Vue Router & State]
      
      Vue --> SPAs[📱 Single Page Applications]
      Vuetify --> LandingPages[🎨 Landing Pages Otimizadas]
      VueRouter --> InsurancePlatforms[🛡️ Plataformas de Seguros]
      
      SPAs --> SuthubProjects[📋 Projetos SUTHUB]
      LandingPages --> SuthubProjects
      InsurancePlatforms --> SuthubProjects
      
      SuthubProjects --> MAPFRE[🏛️ MAPFRE - Seguros Gerais]
      SuthubProjects --> APet[🐕 APet - Seguros Pet]
      SuthubProjects --> SagaBike[🚴 SagaBike - Seguros Bike]
      
      MAPFRE --> CheckoutFlow[💳 Checkout Customizado]
      APet --> CheckoutFlow
      SagaBike --> CheckoutFlow
      
      CheckoutFlow --> PythonIntro[🐍 Introdução ao Python]
      PythonIntro --> PDFGeneration[📄 Geração Dinâmica de PDFs]
      PDFGeneration --> LegacyCode[🔧 Manutenção Código Legado]
      
      %% Transição para Klever
      LegacyCode --> KleverTransition[🔄 Evolução para Web3]
      KleverTransition --> KLEVER([🔗 Klever.io<br/>Blockchain & Crypto])
      
      %% KLEVER - Web3 e Blockchain
      KLEVER --> ReactEco[⚛️ Ecossistema React]
      ReactEco --> ReactJS[⚛️ React.js & Hooks]
      ReactEco --> ReactQuery[🔄 React Query & Cache]
      ReactEco --> ReactTesting[🧪 Testing Library]
      
      ReactJS --> Web3Integration[🌐 Integração Web3]
      Web3Integration --> WalletConnect[💰 Conexão com Carteiras]
      Web3Integration --> BlockchainTx[⛓️ Transações Blockchain]
      
      ReactQuery --> Performance[⚡ Otimização Performance]
      Performance --> KleverProjects[📋 Projetos Klever]
      
      KleverProjects --> KleverSwap[🔄 Klever Swap DEX]
      KleverProjects --> KleverExchange[📈 Klever Exchange]
      
      KleverSwap --> SwapFeatures[💱 Funcionalidades Swap]
      KleverExchange --> ExchangeFeatures[📊 Funcionalidades Exchange]
      
      SwapFeatures --> TestingPractices[🧪 Práticas de Testes]
      ExchangeFeatures --> TestingPractices
      
      TestingPractices --> UnitTests[🔬 Testes Unitários]
      TestingPractices --> E2ETests[🎭 Testes End-to-End]
      TestingPractices --> CICD1[🚀 CI/CD Pipelines]
      
      UnitTests --> ErrorMonitoring[🐛 Monitoramento - Bugsnag]
      E2ETests --> ErrorMonitoring
      CICD1 --> ErrorMonitoring
      
      ErrorMonitoring --> PerformanceOpt[⚡ Otimização Performance & Memória]
      
      %% Transição para Midas Gestor
      PerformanceOpt --> MidasTransition[🔄 Evolução para SaaS]
      MidasTransition --> MIDAS([💼 Midas Gestor<br/>Gestão Rep. Comerciais])
      
      %% MIDAS - SaaS e Backend
      MIDAS --> GoLang[🐹 GoLang Backend]
      GoLang --> APIDesign[🔌 Design APIs RESTful]
      GoLang --> DatabaseDesign[🗄️ Design Database PostgreSQL]
      GoLang --> Authentication[🔐 Autenticação JWT]
      
      APIDesign --> SvelteKit[🚀 SvelteKit Frontend]
      DatabaseDesign --> SvelteKit
      Authentication --> SvelteKit
      
      SvelteKit --> FullStackDev[🌐 Desenvolvimento Full-Stack]
      FullStackDev --> MidasFeatures[📋 Features Midas]
      
      MidasFeatures --> CatalogManagement[📦 Gestão Catálogo Produtos]
      MidasFeatures --> PDFReports[📄 Geração Relatórios PDF]
      MidasFeatures --> DataImport[📊 Sistemas Importação Dados]
      MidasFeatures --> ClientManagement[👥 Gestão de Clientes]
      
      CatalogManagement --> AtomicTransition[🔄 Evolução para Investimentos]
      PDFReports --> AtomicTransition
      DataImport --> AtomicTransition
      ClientManagement --> AtomicTransition
      
      %% Atomic Fund - Atual
      AtomicTransition --> ATOMIC([💰 Atomic Fund<br/>Plataforma Investimentos])
      
      ATOMIC --> NextJS[⚛️ Next.js 15 & React 19]
      NextJS --> AdvancedReact[🧠 Padrões Avançados React]
      NextJS --> TailwindAdvanced[🎨 Tailwind CSS Avançado]
      NextJS --> TypeScriptAdvanced[📘 TypeScript Avançado]
      
      AdvancedReact --> ModernFeatures[🚀 Features Modernas]
      TailwindAdvanced --> ModernFeatures
      TypeScriptAdvanced --> ModernFeatures
      
      ModernFeatures --> InvestmentDashboard[📈 Dashboard Investimentos]
      ModernFeatures --> UserPortfolio[💼 Gestão Portfolio Usuário]
      ModernFeatures --> RealTimeData[📊 Integração Dados Real-time]
      
      InvestmentDashboard --> FutureGoals[🎯 Objetivos Futuros]
      UserPortfolio --> FutureGoals
      RealTimeData --> FutureGoals
      
      FutureGoals --> Leadership[👑 Liderança Técnica]
      FutureGoals --> Innovation[💡 Inovação & P&D]
      FutureGoals --> Mentorship[🎓 Mentoria & Ensino]
      
      %% Estilização
      classDef startEndStyle fill:#e1f5fe,stroke:#01579b,stroke-width:3px
      classDef foundationsStyle fill:#f3e5f5,stroke:#4a148c,stroke-width:2px
      classDef suthubStyle fill:#e8f5e8,stroke:#1b5e20,stroke-width:2px
      classDef kleverStyle fill:#fff3e0,stroke:#e65100,stroke-width:2px
      classDef midasStyle fill:#f1f8e9,stroke:#33691e,stroke-width:2px
      classDef atomicStyle fill:#fce4ec,stroke:#880e4f,stroke-width:2px
      
      class Start,FutureGoals,Leadership,Innovation,Mentorship startEndStyle
      class Basics,HTML,CSS,JS,WebFund,FirstProjects,Freelance foundationsStyle
      class SUTHUB,VueEco,Vue,Vuetify,VueRouter,SPAs,LandingPages,InsurancePlatforms,SuthubProjects,MAPFRE,APet,SagaBike,CheckoutFlow,PythonIntro,PDFGeneration,LegacyCode suthubStyle
      class KLEVER,ReactEco,ReactJS,ReactQuery,ReactTesting,Web3Integration,WalletConnect,BlockchainTx,Performance,KleverProjects,KleverSwap,KleverExchange,SwapFeatures,ExchangeFeatures,TestingPractices,UnitTests,E2ETests,CICD1,ErrorMonitoring,PerformanceOpt kleverStyle
      class MIDAS,GoLang,APIDesign,DatabaseDesign,Authentication,SvelteKit,FullStackDev,MidasFeatures,CatalogManagement,PDFReports,DataImport,ClientManagement midasStyle
      class ATOMIC,NextJS,AdvancedReact,TailwindAdvanced,TypeScriptAdvanced,ModernFeatures,InvestmentDashboard,UserPortfolio,RealTimeData atomicStyle
    `;

	onMount(async () => {
		try {
			mermaid.initialize(mermaidConfig);

			await new Promise((resolve) => setTimeout(resolve, 0));

			const { svg } = await mermaid.render('roadmap-diagram', roadmapDefinition);

			if (mermaidContainer) {
				mermaidContainer.innerHTML = svg;
				isLoading = false;
			}
		} catch (error) {
			console.error('Erro ao renderizar diagrama Mermaid:', error);
			isLoading = false;
		}
	});

	function handleMouseDown(e: MouseEvent) {
		if (!scrollContainer) return;

		isDragging = true;
		startX = e.pageX - scrollContainer.offsetLeft;
		startY = e.pageY - scrollContainer.offsetTop;
		scrollLeft = scrollContainer.scrollLeft;
		scrollTop = scrollContainer.scrollTop;
		scrollContainer.style.cursor = 'grabbing';
		scrollContainer.style.userSelect = 'none';
	}

	function handleMouseMove(e: MouseEvent) {
		if (!isDragging || !scrollContainer) return;

		e.preventDefault();
		const x = e.pageX - scrollContainer.offsetLeft;
		const y = e.pageY - scrollContainer.offsetTop;
		const walkX = (x - startX) * 2;
		const walkY = (y - startY) * 2;
		scrollContainer.scrollLeft = scrollLeft - walkX;
		scrollContainer.scrollTop = scrollTop - walkY;
	}

	function handleMouseUp() {
		if (!scrollContainer) return;

		isDragging = false;
		scrollContainer.style.cursor = 'grab';
		scrollContainer.style.userSelect = 'auto';
	}

	function handleMouseLeave() {
		if (isDragging && scrollContainer) {
			isDragging = false;
			scrollContainer.style.cursor = 'grab';
			scrollContainer.style.userSelect = 'auto';
		}
	}
</script>

<div class="h-fit w-full" in:fade>
	<div class="mt-6 flex justify-center">
		<div class="relative">
			<div
				class="size-32 overflow-hidden rounded-full border-2 border-white/20 bg-white/10 shadow-xl backdrop-blur-md"
			>
				<img
					src="https://ik.imagekit.io/og7loqgh2/the-roadmap.png?updatedAt=1751397022555"
					alt={globals.texts().experienceRoadmap.imageAlt}
					class="h-full w-full object-cover"
				/>
			</div>
		</div>
	</div>

	<div class="mt-3 text-center">
		<p class="mx-auto max-w-lg text-sm text-gray-600">
			{globals.texts().experienceRoadmap.description}
			<span class="mt-1 block font-medium text-gray-700">
				{globals.texts().experienceRoadmap.subtitle}
			</span>
		</p>
	</div>

	{#if isLoading}
		<div class="loading">
			<div class="spinner"></div>
			<p>{globals.texts().experienceRoadmap.loadingText}</p>
		</div>
	{/if}

	<div
		class="mt-3 h-[450px] overflow-x-hidden overflow-y-auto rounded-xl bg-white px-5"
		class:hidden={isLoading}
		bind:this={scrollContainer}
		onmousedown={handleMouseDown}
		onmousemove={handleMouseMove}
		onmouseup={handleMouseUp}
		onmouseleave={handleMouseLeave}
		style="cursor: grab; user-select: none;"
		role="button"
		tabindex="0"
	>
		<div bind:this={mermaidContainer} class="mermaid-diagram"></div>
	</div>

	<div class="roadmap-legend">
		<div class="legend-item">
			<span class="legend-color start-end"></span>
			<span>{globals.texts().experienceRoadmap.legend.startEnd}</span>
		</div>
		<div class="legend-item">
			<span class="legend-color foundations"></span>
			<span>{globals.texts().experienceRoadmap.legend.foundations}</span>
		</div>
		<div class="legend-item">
			<span class="legend-color suthub"></span>
			<span>{globals.texts().experienceRoadmap.legend.suthub}</span>
		</div>
		<div class="legend-item">
			<span class="legend-color klever"></span>
			<span>{globals.texts().experienceRoadmap.legend.klever}</span>
		</div>
		<div class="legend-item">
			<span class="legend-color midas"></span>
			<span>{globals.texts().experienceRoadmap.legend.midas}</span>
		</div>
		<div class="legend-item">
			<span class="legend-color atomic"></span>
			<span>{globals.texts().experienceRoadmap.legend.atomic}</span>
		</div>
	</div>
</div>

<style>
	.loading {
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		padding: 60px 20px;
		color: #666;
	}

	.spinner {
		width: 40px;
		height: 40px;
		border: 4px solid #f3f3f3;
		border-top: 4px solid #007acc;
		border-radius: 50%;
		animation: spin 1s linear infinite;
		margin-bottom: 15px;
	}

	@keyframes spin {
		0% {
			transform: rotate(0deg);
		}
		100% {
			transform: rotate(360deg);
		}
	}

	.mermaid-diagram {
		width: 100%;
		text-align: center;
	}

	/* Melhorar aparência dos elementos SVG do Mermaid */
	:global(.mermaid-diagram svg) {
		max-width: 100%;
		height: auto;
	}

	:global(.mermaid-diagram .node rect),
	:global(.mermaid-diagram .node circle),
	:global(.mermaid-diagram .node ellipse),
	:global(.mermaid-diagram .node polygon) {
		filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
	}

	:global(.mermaid-diagram .edgePath path) {
		stroke-width: 2px;
	}

	.roadmap-legend {
		display: flex;
		flex-wrap: wrap;
		justify-content: center;
		gap: 20px;
		padding: 20px;
		background: #f8f9fa;
		border-radius: 8px;
		margin-top: 20px;
	}

	.legend-item {
		display: flex;
		align-items: center;
		gap: 8px;
		font-size: 0.9rem;
		color: #555;
	}

	.legend-color {
		width: 16px;
		height: 16px;
		border-radius: 3px;
		border: 2px solid;
	}

	.legend-color.start-end {
		background: #e1f5fe;
		border-color: #01579b;
	}

	/* Responsividade */
	@media (max-width: 768px) {
		.roadmap-legend {
			flex-direction: column;
			align-items: center;
		}
	}
</style>
