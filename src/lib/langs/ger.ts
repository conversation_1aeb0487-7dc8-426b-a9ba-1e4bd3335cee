import type { LangContentStructure } from ".";

export const GERMAN_CONTENT: LangContentStructure = {
    experienceRoadmap: {
        imageAlt: "Tarcísio AI-Assistent",
        description: "Meine berufliche Reise in der Technologie, von den ersten Schritten bis zu aktuellen Erfolgen.",
        subtitle: "Eine Roadmap meiner Entwicklung in der Tech-Karriere.",
        loadingText: "Roadmap wird geladen...",
        legend: {
            startEnd: "Start/Zukunft",
            foundations: "Grundlagen (2021)",
            suthub: "SUTHUB (2022)",
            klever: "Klever.io",
            midas: "Midas Gestor",
            atomic: "Atomic Fund (Aktuell)"
        }
    },
    projectsList: {
        imageAlt: "Tarcísio Projekte",
        description: "Eine Auswahl meiner relevantesten Projekte, die Vielseitigkeit und technische Expertise demonstrieren.",
        viewProject: "Projekt ansehen",
        projects: {
            soraizReservas: {
                title: "Soraiz Reservas",
                description: "Vollständige Reservierungsplattform inspiriert von Airbnb, mit Verwaltungspanel für Reservierungsmanagement und internem Content-Management-System. Vollständig mit SvelteKit und Supabase entwickelt."
            },
            eventia: {
                title: "Eventia",
                description: "Innovative Plattform zur Erstellung von Events mit künstlicher Intelligenz. Bietet schnelle Event-Erstellung, Einladungsversandsystem und Geschenkkatalog mit integriertem Checkout. Backend in Golang und Frontend in SvelteKit."
            },
            ibiCash: {
                title: "IbiCash",
                description: "Landtokenisierungsplattform mit interaktiver Kartenintegration über Mapbox. Frontend in Next.js entwickelt mit aktiver Beteiligung an der App-Entwicklung und Erstellung der interaktiven Landing Page."
            },
            midasGestor: {
                title: "Midas Gestor",
                description: "Managementplattform spezialisiert für Handelsvertreter. SvelteKit-Frontend integriert mit Golang-Backend, einschließlich PDF-Berichtgenerierung, Datenimport und vollständiger Katalogverwaltung."
            },
            cryptoTracker: {
                title: "CryptoTracker",
                description: "Erweiterte Plattform für die Verwaltung kryptografischer Assets von mehreren Börsen. Funktionen umfassen Saldovisualisierung, Leistungsvergleich zwischen Börsen und sichere OTP-Authentifizierung."
            },
            regenteApp: {
                title: "Regente App",
                description: "Innovatives App-Konzept mit integriertem LLM-System für intelligentes Verständnis von Eigentümergemeinschaftsordnungen und anderen Dokumenten. Backend optimiert für Token-Ökonomie, vollständig von SvelteKit verwaltet."
            }
        }
    },
    smartOverview: {
        imageAlt: "Tarcísio AI-Assistent",
        description: "Nutzen Sie diesen KI-integrierten Chat, um Fragen zu meiner beruflichen Erfahrung zu stellen. Erhalten Sie vereinfachte und direkte Antworten über meine Karriere, Projekte und Fähigkeiten.",
        questionLabel: "Stellen Sie Ihre Frage:",
        questionPlaceholder: "Z.B.: Was ist Tarcísios Erfahrung in React?",
        examplesTitle: "Beispielfragen:",
        responseText: "Antworten erscheinen hier...",
        exampleQuestions: {
            experience: "Was ist Tarcísios Erfahrung in der Entwicklung?",
            technologies: "Welche Technologien beherrscht er?",
            projects: "Erzählen Sie von seinen relevantesten Projekten"
        }
    }
}