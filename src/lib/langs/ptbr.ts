import type { LangContentStructure } from ".";

export const PORTUGUESE_CONTENT: LangContentStructure = {
    experienceRoadmap: {
        imageAlt: "Tarcísio AI Assistant",
        description: "Minha jornada profissional em tecnologia, desde os primeiros passos até conquistas atuais.",
        subtitle: "Um roadmap da minha evolução na carreira tech.",
        loadingText: "Carregando roadmap...",
        legend: {
            startEnd: "Início/Futuro",
            foundations: "Fundamentos (2021)",
            suthub: "SUTHUB (2022)",
            klever: "Klever.io",
            midas: "Midas Gestor",
            atomic: "Atomic Fund (Atual)"
        }
    },
    projectsList: {
        imageAlt: "Tarcísio Projects",
        description: "Uma seleção dos meus projetos mais relevantes, demonstrando versatilidade e expertise técnica.",
        viewProject: "Ver projeto",
        projects: {
            soraizReservas: {
                title: "<PERSON><PERSON>z Reservas",
                description: "Plataforma completa de reservas inspirada no Airbnb, com painel administrativo para gestão de reservas e sistema interno de gerenciamento de conteúdo. Desenvolvida inteiramente com SvelteKit e Supabase."
            },
            eventia: {
                title: "Eventia",
                description: "Plataforma inovadora para criação de eventos com inteligência artificial. Oferece criação rápida de eventos, sistema de envio de convites e catálogo de presentes com checkout integrado. Backend em Golang e frontend em SvelteKit."
            },
            ibiCash: {
                title: "IbiCash",
                description: "Plataforma de tokenização de terrenos com integração de mapas interativos via Mapbox. Frontend desenvolvido em Next.js com participação ativa na construção do aplicativo e criação da landing page interativa."
            },
            midasGestor: {
                title: "Midas Gestor",
                description: "Plataforma de gerenciamento especializada para representantes comerciais. Frontend em SvelteKit integrado com backend em Golang, incluindo geração de relatórios em PDF, importação de dados e gestão completa de catálogo."
            },
            cryptoTracker: {
                title: "CryptoTracker",
                description: "Plataforma avançada de gerenciamento de ativos criptográficos de múltiplas exchanges. Recursos incluem visualização de saldos, comparação de performance entre exchanges e autenticação segura com OTP."
            },
            regenteApp: {
                title: "Regente App",
                description: "Conceito inovador de aplicativo com sistema LLM integrado para compreensão inteligente de regimentos condominiais e outros documentos. Backend otimizado para economia de tokens, totalmente gerenciado pelo SvelteKit."
            }
        }
    },
    smartOverview: {
        imageAlt: "Tarcísio AI Assistant",
        description: "Use este chat integrado com IA para fazer perguntas sobre minha experiência profissional. Receba respostas simplificadas e diretas sobre minha carreira, projetos e habilidades.",
        questionLabel: "Faça sua pergunta:",
        questionPlaceholder: "Ex: Qual é a experiência do Tarcísio em React?",
        examplesTitle: "Exemplos de perguntas:",
        responseText: "As respostas aparecerão aqui...",
        exampleQuestions: {
            experience: "Qual é a experiência do Tarcísio em desenvolvimento?",
            technologies: "Quais tecnologias ele domina?",
            projects: "Conte sobre os projetos mais relevantes dele"
        }
    }
}