import { type LangContentStructure } from "."

export const ENGLISH_CONTENT: LangContentStructure = {
    experienceRoadmap: {
        imageAlt: "Tarcísio AI Assistant",
        description: "My professional journey in technology, from the first steps to current achievements.",
        subtitle: "A roadmap of my evolution in the tech career.",
        loadingText: "Loading roadmap...",
        legend: {
            startEnd: "Start/Future",
            foundations: "Foundations (2021)",
            suthub: "SUTHUB (2022)",
            klever: "Klever.io",
            midas: "Midas Gestor",
            atomic: "Atomic Fund (Current)"
        }
    },
    projectsList: {
        imageAlt: "Tarcísio Projects",
        description: "A selection of my most relevant projects, demonstrating versatility and technical expertise.",
        viewProject: "View project",
        projects: {
            soraizReservas: {
                title: "Soraiz Reservas",
                description: "Complete reservation platform inspired by Airbnb, with administrative panel for reservation management and internal content management system. Developed entirely with SvelteKit and Supabase."
            },
            eventia: {
                title: "Eventia",
                description: "Innovative platform for creating events with artificial intelligence. Offers quick event creation, invitation sending system and gift catalog with integrated checkout. Backend in Golang and frontend in SvelteKit."
            },
            ibiCash: {
                title: "IbiCash",
                description: "Land tokenization platform with interactive map integration via Mapbox. Frontend developed in Next.js with active participation in app development and creation of interactive landing page."
            },
            midasGestor: {
                title: "Midas Gestor",
                description: "Management platform specialized for commercial representatives. SvelteKit frontend integrated with Golang backend, including PDF report generation, data import and complete catalog management."
            },
            cryptoTracker: {
                title: "CryptoTracker",
                description: "Advanced platform for managing cryptographic assets from multiple exchanges. Features include balance visualization, performance comparison between exchanges and secure OTP authentication."
            },
            regenteApp: {
                title: "Regente App",
                description: "Innovative app concept with integrated LLM system for intelligent understanding of condominium bylaws and other documents. Backend optimized for token economy, fully managed by SvelteKit."
            }
        }
    },
    smartOverview: {
        imageAlt: "Tarcísio AI Assistant",
        description: "Use this AI-integrated chat to ask questions about my professional experience. Get simplified and direct answers about my career, projects and skills.",
        questionLabel: "Ask your question:",
        questionPlaceholder: "Ex: What is Tarcísio's experience in React?",
        examplesTitle: "Example questions:",
        responseText: "Answers will appear here...",
        exampleQuestions: {
            experience: "What is Tarcísio's experience in development?",
            technologies: "What technologies does he master?",
            projects: "Tell me about his most relevant projects"
        }
    }
}