export interface TrackingRecord {
    created: string;
    updated: string;
    action: string;
    metadata?: Record<string, any>;
    user_id?: string;
    session_id?: string;
    user_agent?: string;
    ip_address?: string;
    page_url?: string;
}

export class TrackingService {
    private sessionId: string;

    constructor() {
        this.sessionId = Math.random().toString(36).substring(2) + Date.now().toString(36);
    }

    trackAction(action: string, metadata?: Record<string, any>, userId?: string, waitLimit?: number, notify?: boolean) {
        const event: TrackingRecord = {
            action,
            metadata,
            session_id: this.sessionId,
            created: new Date().toISOString(),
            updated: new Date().toISOString(),
            user_id: userId || undefined,
            page_url: window.location.href,
            user_agent: navigator.userAgent,
        };

        if (waitLimit) {
            return new Promise((resolve) => {
                setTimeout(() => {
                    this.sendToServer(event);
                    resolve(event);
                }, waitLimit);
            });
        }

        if (notify) {
            this.notifyOnDiscord([
                { title: 'New Tracking Event', message: `Action: ${action}, Metadata: ${JSON.stringify(metadata)}` }
            ]);
        }

        this.sendToServer(event);
    }

    async sendToServer(trackingRecord: TrackingRecord) {
        try {
            const response = await fetch('/api/tools/actions', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify(trackingRecord)
            });

            if (!response.ok) {
                throw new Error('Failed to send tracking data');
            }

            return await response.json();
        } catch (error) {
            console.error('Error sending tracking data:', error);
        }
    }

    async notifyOnDiscord(messages: { title: string; message: string, color?: string }[]) {
        try {
            const response = await fetch('/api/tools/events', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ messages })
            });

            if (!response.ok) {
                throw new Error('Failed to send notification');
            }

            return await response.json();
        } catch (error) {
            console.error('Error sending notification:', error);
        }
    }
}

const trackingService = new TrackingService();
export default trackingService;